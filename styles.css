/* Baseline Analytics Custom Styles */

/* View Toggle Buttons */
.view-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.view-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.view-btn.active {
    background-color: #0EA5E9;
    border-color: #0EA5E9;
}

/* Swiper Customization */
.mobileSwiper {
    padding-bottom: 50px;
}

.swiper-pagination-bullet {
    background-color: #0EA5E9;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background-color: #1E40AF;
}

/* Metric Cards */
.metric-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1E40AF;
}

.metric-label {
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.metric-trend {
    font-size: 0.875rem;
    font-weight: 600;
}

.trend-up {
    color: #10b981;
}

.trend-down {
    color: #ef4444;
}

.trend-stable {
    color: #6b7280;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.chart-tooltip {
    background: rgba(44, 44, 44, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Table Styles */
#playersTable {
    border-collapse: separate;
    border-spacing: 0;
}

#playersTable th {
    background-color: #f8fafc;
    color: #374151;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
}

#playersTable td {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
}

#playersTable tbody tr:hover {
    background-color: #f8fafc;
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.player-name {
    font-weight: 600;
    color: #1f2937;
}

.player-details {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.trend-improving {
    background-color: #dcfce7;
    color: #166534;
}

.trend-declining {
    background-color: #fee2e2;
    color: #991b1b;
}

.trend-stable {
    background-color: #f3f4f6;
    color: #374151;
}

/* Action Buttons */
.action-btn {
    background-color: #0EA5E9;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #0284c7;
    transform: translateY(-1px);
}

/* Leaderboard Styles */
.leaderboard-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #0EA5E9;
}

.leaderboard-rank {
    font-weight: 700;
    color: #1E40AF;
    font-size: 1.125rem;
}

.leaderboard-player {
    flex: 1;
    margin-left: 12px;
}

.leaderboard-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

.leaderboard-value {
    font-weight: 700;
    color: #0EA5E9;
}

/* Modal Styles */
#playerModal {
    backdrop-filter: blur(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .metric-value {
        font-size: 2rem;
    }
    
    .view-btn {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    .view-btn i {
        display: none;
    }
}

@media (max-width: 640px) {
    .view-btn span {
        display: none;
    }
    
    .view-btn i {
        display: inline-block;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Chart Point Hover Effects */
.chart-point {
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-point:hover {
    transform: scale(1.2);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus States */
select:focus,
input:focus {
    outline: none;
    ring: 2px;
    ring-color: #0EA5E9;
    ring-opacity: 0.5;
}

/* Sortable Table Headers */
.sortable:hover {
    background-color: #e5e7eb;
}

.sort-asc::after {
    content: ' ↑';
    color: #0EA5E9;
}

.sort-desc::after {
    content: ' ↓';
    color: #0EA5E9;
}
