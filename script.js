// Task Manager Application
class TaskManager {
    constructor() {
        this.tasks = JSON.parse(localStorage.getItem('flashy-tasks')) || [];
        this.currentFilter = 'all';
        this.taskIdCounter = this.tasks.length > 0 ? Math.max(...this.tasks.map(t => t.id)) + 1 : 1;
        
        this.initializeElements();
        this.bindEvents();
        this.render();
        this.updateStats();
    }
    
    initializeElements() {
        this.taskInput = document.getElementById('taskInput');
        this.categorySelect = document.getElementById('categorySelect');
        this.addTaskBtn = document.getElementById('addTaskBtn');
        this.tasksList = document.getElementById('tasksList');
        this.emptyState = document.getElementById('emptyState');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.successModal = document.getElementById('successModal');
        this.closeModalBtn = document.getElementById('closeModal');
        this.completionMessage = document.getElementById('completionMessage');
        
        // Stats elements
        this.totalTasksEl = document.getElementById('totalTasks');
        this.completedTasksEl = document.getElementById('completedTasks');
        this.pendingTasksEl = document.getElementById('pendingTasks');
    }
    
    bindEvents() {
        this.addTaskBtn.addEventListener('click', () => this.addTask());
        this.taskInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addTask();
        });
        
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.setFilter(e.target.dataset.category));
        });
        
        this.closeModalBtn.addEventListener('click', () => this.closeModal());
        this.successModal.addEventListener('click', (e) => {
            if (e.target === this.successModal) this.closeModal();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.closeModal();
        });
    }
    
    addTask() {
        const text = this.taskInput.value.trim();
        const category = this.categorySelect.value;
        
        if (!text) {
            this.taskInput.focus();
            this.taskInput.style.borderColor = '#ff5252';
            setTimeout(() => {
                this.taskInput.style.borderColor = '#e1e5e9';
            }, 1000);
            return;
        }
        
        const task = {
            id: this.taskIdCounter++,
            text: text,
            category: category,
            completed: false,
            createdAt: new Date().toISOString(),
            completedAt: null
        };
        
        this.tasks.unshift(task);
        this.saveToStorage();
        this.taskInput.value = '';
        this.render();
        this.updateStats();
        
        // Add task with animation
        setTimeout(() => {
            const taskElement = this.tasksList.querySelector(`[data-task-id="${task.id}"]`);
            if (taskElement) {
                taskElement.style.animation = 'slideIn 0.5s ease';
            }
        }, 50);
    }
    
    toggleTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        const taskElement = this.tasksList.querySelector(`[data-task-id="${taskId}"]`);
        
        if (!task.completed) {
            // Mark as completed with flashy animation
            task.completed = true;
            task.completedAt = new Date().toISOString();
            
            // Add completing animation
            taskElement.classList.add('completing');
            
            // Create particle burst effect
            const particleBurst = new ParticleBurst(taskElement);
            particleBurst.create();
            
            // Show confetti after a short delay
            setTimeout(() => {
                if (confettiSystem) {
                    const rect = taskElement.getBoundingClientRect();
                    confettiSystem.burst(rect.left + rect.width / 2, rect.top + rect.height / 2, 30);
                }
                
                // Show success modal
                this.showSuccessModal(task);
            }, 300);
            
        } else {
            // Mark as incomplete
            task.completed = false;
            task.completedAt = null;
        }
        
        this.saveToStorage();
        this.render();
        this.updateStats();
    }
    
    deleteTask(taskId) {
        const taskElement = this.tasksList.querySelector(`[data-task-id="${taskId}"]`);
        
        // Add slide out animation
        if (taskElement) {
            taskElement.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                this.tasks = this.tasks.filter(t => t.id !== taskId);
                this.saveToStorage();
                this.render();
                this.updateStats();
            }, 300);
        }
    }
    
    setFilter(category) {
        this.currentFilter = category;
        
        // Update active filter button
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });
        
        this.render();
    }
    
    getFilteredTasks() {
        if (this.currentFilter === 'all') {
            return this.tasks;
        }
        return this.tasks.filter(task => task.category === this.currentFilter);
    }
    
    render() {
        const filteredTasks = this.getFilteredTasks();
        
        if (filteredTasks.length === 0) {
            this.tasksList.style.display = 'none';
            this.emptyState.style.display = 'block';
        } else {
            this.tasksList.style.display = 'block';
            this.emptyState.style.display = 'none';
            
            this.tasksList.innerHTML = filteredTasks.map(task => this.createTaskHTML(task)).join('');
            
            // Bind events for task items
            this.tasksList.querySelectorAll('.task-item').forEach(item => {
                const taskId = parseInt(item.dataset.taskId);
                const checkbox = item.querySelector('.task-checkbox');
                const deleteBtn = item.querySelector('.delete-btn');
                
                checkbox.addEventListener('click', () => this.toggleTask(taskId));
                deleteBtn.addEventListener('click', () => this.deleteTask(taskId));
            });
        }
    }
    
    createTaskHTML(task) {
        const categoryEmojis = {
            work: '🏢',
            personal: '👤',
            health: '💪',
            learning: '📚',
            shopping: '🛒',
            other: '📝'
        };
        
        const timeAgo = this.getTimeAgo(task.createdAt);
        
        return `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-task-id="${task.id}">
                <div class="task-checkbox ${task.completed ? 'checked' : ''}">
                    ${task.completed ? '<i class="fas fa-check"></i>' : ''}
                </div>
                <div class="task-content">
                    <div class="task-text">${this.escapeHtml(task.text)}</div>
                    <div class="task-meta">
                        <span class="category-badge category-${task.category}">
                            ${categoryEmojis[task.category]} ${task.category}
                        </span>
                        <span class="task-time">${timeAgo}</span>
                        ${task.completed ? `<span class="completed-time">✅ Completed ${this.getTimeAgo(task.completedAt)}</span>` : ''}
                    </div>
                </div>
                <button class="delete-btn" title="Delete task">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }
    
    showSuccessModal(task) {
        const messages = [
            "Awesome! You're crushing it! 🎉",
            "Great job! Keep up the momentum! 🚀",
            "Task completed! You're on fire! 🔥",
            "Well done! Progress feels good! ⭐",
            "Fantastic! Another one bites the dust! 💪",
            "Excellent work! You're unstoppable! 🌟"
        ];
        
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.completionMessage.textContent = randomMessage;
        
        this.successModal.style.display = 'block';
        
        // Auto-close after 3 seconds
        setTimeout(() => {
            this.closeModal();
        }, 3000);
    }
    
    closeModal() {
        this.successModal.style.display = 'none';
    }
    
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const pending = total - completed;
        
        this.animateNumber(this.totalTasksEl, total);
        this.animateNumber(this.completedTasksEl, completed);
        this.animateNumber(this.pendingTasksEl, pending);
    }
    
    animateNumber(element, targetNumber) {
        const currentNumber = parseInt(element.textContent) || 0;
        const increment = targetNumber > currentNumber ? 1 : -1;
        const duration = 300;
        const steps = Math.abs(targetNumber - currentNumber);
        const stepDuration = steps > 0 ? duration / steps : 0;
        
        if (steps === 0) return;
        
        let current = currentNumber;
        const timer = setInterval(() => {
            current += increment;
            element.textContent = current;
            
            if (current === targetNumber) {
                clearInterval(timer);
            }
        }, stepDuration);
    }
    
    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    saveToStorage() {
        localStorage.setItem('flashy-tasks', JSON.stringify(this.tasks));
    }
}

// Add slide out animation CSS
const additionalStyle = document.createElement('style');
additionalStyle.textContent = `
    @keyframes slideOut {
        0% {
            opacity: 1;
            transform: translateX(0);
        }
        100% {
            opacity: 0;
            transform: translateX(-100%);
        }
    }
`;
document.head.appendChild(additionalStyle);

// Initialize the task manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TaskManager();
});
