// Main Application Controller
class BaselineApp {
    constructor() {
        this.currentView = 'mobile';
        this.isDataLoaded = false;
        this.views = {
            mobile: mobileView,
            tablet: tabletView,
            desktop: desktopView
        };
    }

    async init() {
        try {
            // Show loading state
            this.showLoading();

            // Load data
            const dataLoaded = await dataManager.loadData();
            if (!dataLoaded) {
                throw new Error('Failed to load player data');
            }

            this.isDataLoaded = true;

            // Initialize views
            this.initializeViews();

            // Set up view switching
            this.setupViewSwitching();

            // Set up responsive behavior
            this.setupResponsiveHandling();

            // Show initial view
            this.showView('mobile');

            // Hide loading state
            this.hideLoading();

            console.log('Baseline Analytics Demo initialized successfully');
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load the application. Please refresh the page.');
        }
    }

    initializeViews() {
        Object.values(this.views).forEach(view => {
            if (view && typeof view.init === 'function') {
                view.init();
            }
        });
    }

    setupViewSwitching() {
        const viewButtons = {
            'mobileViewBtn': 'mobile',
            'tabletViewBtn': 'tablet',
            'desktopViewBtn': 'desktop'
        };

        Object.entries(viewButtons).forEach(([buttonId, viewName]) => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', () => {
                    this.showView(viewName);
                });
            }
        });
    }

    showView(viewName) {
        if (!this.isDataLoaded) return;

        // Update current view
        this.currentView = viewName;

        // Hide all views
        document.querySelectorAll('.view-container').forEach(container => {
            container.classList.add('hidden');
        });

        // Show selected view
        const viewContainer = document.getElementById(`${viewName}View`);
        if (viewContainer) {
            viewContainer.classList.remove('hidden');
            viewContainer.classList.add('fade-in');
        }

        // Update active button
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeButton = document.getElementById(`${viewName}ViewBtn`);
        if (activeButton) {
            activeButton.classList.add('active');
        }

        // Trigger view-specific initialization if needed
        this.onViewChange(viewName);

        // Update URL hash for bookmarking
        window.location.hash = viewName;
    }

    onViewChange(viewName) {
        // Handle any view-specific logic when switching views
        switch (viewName) {
            case 'mobile':
                // Mobile view specific logic
                if (mobileView.swiper) {
                    mobileView.swiper.update();
                }
                break;
            case 'tablet':
                // Tablet view specific logic
                tabletView.loadPlayersTable();
                break;
            case 'desktop':
                // Desktop view specific logic
                desktopView.refreshLeaderboards();
                break;
        }
    }

    setupResponsiveHandling() {
        // Auto-switch views based on screen size
        const handleResize = () => {
            const width = window.innerWidth;
            
            // Auto-switch to appropriate view based on screen size
            if (width < 768) {
                // Mobile
                if (this.currentView !== 'mobile') {
                    this.showView('mobile');
                }
            } else if (width < 1024) {
                // Tablet
                if (this.currentView !== 'tablet') {
                    this.showView('tablet');
                }
            } else {
                // Desktop
                if (this.currentView !== 'desktop') {
                    this.showView('desktop');
                }
            }

            // Handle view-specific resize logic
            if (this.views[this.currentView] && typeof this.views[this.currentView].handleResize === 'function') {
                this.views[this.currentView].handleResize();
            }
        };

        // Debounce resize handler
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleResize, 250);
        });

        // Initial responsive check
        handleResize();
    }

    showLoading() {
        const loadingHTML = `
            <div id="loadingScreen" class="fixed inset-0 bg-baseline-cream flex items-center justify-center z-50">
                <div class="text-center">
                    <div class="w-16 h-16 bg-baseline-ocean rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
                        <i class="fas fa-baseball-ball text-white text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-baseline-black mb-2">Baseline Analytics</h2>
                    <p class="text-baseline-black opacity-75">Loading player data...</p>
                    <div class="mt-4">
                        <div class="w-48 h-2 bg-gray-200 rounded-full mx-auto">
                            <div class="h-2 bg-baseline-ocean rounded-full animate-pulse" style="width: 60%"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', loadingHTML);
    }

    hideLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.remove();
        }
    }

    showError(message) {
        const errorHTML = `
            <div id="errorScreen" class="fixed inset-0 bg-baseline-cream flex items-center justify-center z-50">
                <div class="text-center max-w-md mx-auto p-6">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                        <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-baseline-black mb-2">Error</h2>
                    <p class="text-baseline-black opacity-75 mb-4">${message}</p>
                    <button onclick="window.location.reload()" class="bg-baseline-ocean text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        Retry
                    </button>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', errorHTML);
    }

    // Handle browser back/forward navigation
    handleHashChange() {
        const hash = window.location.hash.substring(1);
        if (hash && ['mobile', 'tablet', 'desktop'].includes(hash)) {
            this.showView(hash);
        }
    }

    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when no input is focused
            if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'SELECT') {
                return;
            }

            switch (e.key) {
                case '1':
                    this.showView('mobile');
                    break;
                case '2':
                    this.showView('tablet');
                    break;
                case '3':
                    this.showView('desktop');
                    break;
                case 'r':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        window.location.reload();
                    }
                    break;
            }
        });
    }

    // Analytics tracking (placeholder for real analytics)
    trackEvent(category, action, label = '') {
        console.log(`Analytics: ${category} - ${action} - ${label}`);
        // In a real app, this would send to Google Analytics, Mixpanel, etc.
    }

    // Performance monitoring
    measurePerformance(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`Performance: ${name} took ${(end - start).toFixed(2)}ms`);
        return result;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new BaselineApp();
    
    // Set up hash change handling
    window.addEventListener('hashchange', () => app.handleHashChange());
    
    // Set up keyboard shortcuts
    app.setupKeyboardShortcuts();
    
    // Initialize the app
    app.init();
    
    // Make app globally available for debugging
    window.baselineApp = app;
});

// Service Worker registration for offline capability (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
