// Data Management Module
class DataManager {
    constructor() {
        this.players = [];
        this.benchmarks = {};
        this.currentTimeframe = 'July'; // Default to latest data
    }

    async loadData() {
        try {
            const response = await fetch('./data/mock_players.json');
            const data = await response.json();
            this.players = data.players;
            this.benchmarks = data.benchmarks;
            return true;
        } catch (error) {
            console.error('Error loading data:', error);
            return false;
        }
    }

    getPlayers() {
        return this.players;
    }

    getPlayer(id) {
        return this.players.find(player => player.id === parseInt(id));
    }

    getBenchmark(ageGroup) {
        return this.benchmarks[ageGroup];
    }

    getPlayerMetrics(playerId, timeframe = this.currentTimeframe) {
        const player = this.getPlayer(playerId);
        if (!player || !player.metrics[timeframe]) return null;
        
        return {
            ...player.metrics[timeframe].blast,
            ...player.metrics[timeframe].hittrax
        };
    }

    getPlayerTrend(playerId, metric) {
        const player = this.getPlayer(playerId);
        if (!player) return null;

        const timeframes = ['March', 'May', 'July'];
        const values = timeframes.map(tf => {
            const blastMetrics = player.metrics[tf].blast;
            const hittraxMetrics = player.metrics[tf].hittrax;
            return blastMetrics[metric] || hittraxMetrics[metric];
        }).filter(val => val !== undefined);

        if (values.length < 2) return null;

        const latest = values[values.length - 1];
        const previous = values[values.length - 2];
        const change = ((latest - previous) / previous) * 100;

        let trend = 'stable';
        if (change > 3) trend = 'improving';
        else if (change < -3) trend = 'declining';

        return {
            values,
            timeframes,
            change: change.toFixed(1),
            trend,
            latest,
            previous
        };
    }

    getTrendLabel(trend, change) {
        const icons = {
            improving: '↑',
            declining: '↓',
            stable: '→'
        };

        const labels = {
            improving: 'Improving',
            declining: 'Declining',
            stable: 'Stable'
        };

        return `${icons[trend]} ${labels[trend]}`;
    }

    getLeaderboard(metric, limit = 3) {
        const playersWithMetric = this.players.map(player => {
            const metrics = this.getPlayerMetrics(player.id);
            return {
                ...player,
                value: metrics ? metrics[metric] : 0
            };
        }).filter(player => player.value > 0);

        return playersWithMetric
            .sort((a, b) => b.value - a.value)
            .slice(0, limit);
    }

    filterPlayers(filters) {
        return this.players.filter(player => {
            if (filters.age && player.age !== parseInt(filters.age)) return false;
            if (filters.position && player.position !== filters.position) return false;
            if (filters.name && !player.name.toLowerCase().includes(filters.name.toLowerCase())) return false;
            return true;
        });
    }

    getMetricDisplayName(metric) {
        const displayNames = {
            batSpeed: 'Bat Speed (mph)',
            peakHandSpeed: 'Peak Hand Speed (mph)',
            rotationalAcceleration: 'Rotational Acceleration (g)',
            attackAngle: 'Attack Angle (°)',
            onPlaneEfficiency: 'On Plane Efficiency (%)',
            earlyConnection: 'Early Connection (°)',
            connectionAtImpact: 'Connection at Impact (°)',
            exitVelocityAvg: 'Exit Velocity Avg (mph)',
            exitVelocityMax: 'Exit Velocity Max (mph)',
            distanceAvg: 'Distance Avg (ft)',
            distanceMax: 'Distance Max (ft)',
            hardHitAverage: 'Hard Hit Average'
        };
        return displayNames[metric] || metric;
    }

    getMetricUnit(metric) {
        const units = {
            batSpeed: 'mph',
            peakHandSpeed: 'mph',
            rotationalAcceleration: 'g',
            attackAngle: '°',
            onPlaneEfficiency: '%',
            earlyConnection: '°',
            connectionAtImpact: '°',
            exitVelocityAvg: 'mph',
            exitVelocityMax: 'mph',
            distanceAvg: 'ft',
            distanceMax: 'ft',
            hardHitAverage: ''
        };
        return units[metric] || '';
    }

    formatMetricValue(value, metric) {
        if (metric === 'hardHitAverage') {
            return value.toFixed(3);
        }
        return value.toFixed(1);
    }

    getBlastMetrics() {
        return ['batSpeed', 'peakHandSpeed', 'rotationalAcceleration', 'attackAngle', 'onPlaneEfficiency', 'earlyConnection', 'connectionAtImpact'];
    }

    getHittraxMetrics() {
        return ['exitVelocityAvg', 'exitVelocityMax', 'distanceAvg', 'distanceMax', 'hardHitAverage'];
    }

    getAllMetrics() {
        return [...this.getBlastMetrics(), ...this.getHittraxMetrics()];
    }

    getTimeframes() {
        return ['March', 'May', 'July'];
    }

    // Utility method to get player comparison data for radar chart
    getPlayerComparison(playerId, benchmarkAge) {
        const player = this.getPlayer(playerId);
        const benchmark = this.getBenchmark(benchmarkAge);
        
        if (!player || !benchmark) return null;

        const playerMetrics = this.getPlayerMetrics(playerId);
        const radarMetrics = ['batSpeed', 'exitVelocityAvg', 'attackAngle', 'onPlaneEfficiency', 'hardHitAverage'];
        
        const playerData = radarMetrics.map(metric => playerMetrics[metric] || 0);
        const benchmarkData = radarMetrics.map(metric => {
            return benchmark.blast[metric] || benchmark.hittrax[metric] || 0;
        });

        return {
            labels: radarMetrics.map(metric => this.getMetricDisplayName(metric)),
            playerData,
            benchmarkData,
            playerName: player.name
        };
    }
}

// Initialize global data manager
const dataManager = new DataManager();
