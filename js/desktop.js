// Desktop View Module
class DesktopView {
    constructor() {
        this.currentPlayer = null;
        this.currentBenchmark = 'age16';
        this.radarChart = null;
    }

    init() {
        this.setupPlayerSelector();
        this.setupBenchmarkSelector();
        this.generateLeaderboards();
        this.bindEvents();
    }

    setupPlayerSelector() {
        const playerSelect = document.getElementById('desktopPlayerSelect');
        const players = dataManager.getPlayers();
        
        playerSelect.innerHTML = '<option value="">Select a player...</option>';
        players.forEach(player => {
            const option = document.createElement('option');
            option.value = player.id;
            option.textContent = `${player.name} - ${player.position}`;
            playerSelect.appendChild(option);
        });
    }

    setupBenchmarkSelector() {
        const benchmarkSelect = document.getElementById('benchmarkAge');
        benchmarkSelect.addEventListener('change', (e) => {
            this.currentBenchmark = e.target.value;
            if (this.currentPlayer) {
                this.updateRadarChart();
            }
        });
    }

    bindEvents() {
        const playerSelect = document.getElementById('desktopPlayerSelect');
        playerSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                this.loadPlayer(parseInt(e.target.value));
            } else {
                this.clearRadarChart();
            }
        });
    }

    loadPlayer(playerId) {
        const player = dataManager.getPlayer(playerId);
        if (!player) return;

        this.currentPlayer = player;
        this.updateRadarChart();
    }

    updateRadarChart() {
        if (!this.currentPlayer) return;

        this.radarChart = chartManager.createPlayerRadarChart(
            'radarChart', 
            this.currentPlayer.id, 
            this.currentBenchmark
        );
    }

    clearRadarChart() {
        this.currentPlayer = null;
        chartManager.destroyChart('radarChart');
        this.radarChart = null;
    }

    generateLeaderboards() {
        this.createLeaderboard('batSpeed', 'batSpeedLeaders', 'Bat Speed');
        this.createLeaderboard('exitVelocityAvg', 'exitVelocityLeaders', 'Exit Velocity');
        this.createLeaderboard('hardHitAverage', 'hardHitLeaders', 'Hard Hit Average');
    }

    createLeaderboard(metric, containerId, title) {
        const container = document.getElementById(containerId);
        const leaders = dataManager.getLeaderboard(metric, 3);
        
        container.innerHTML = '';

        leaders.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item cursor-pointer hover:bg-gray-100 transition-colors';
            
            const unit = dataManager.getMetricUnit(metric);
            const value = dataManager.formatMetricValue(player.value, metric);
            
            item.innerHTML = `
                <div class="leaderboard-rank">#${index + 1}</div>
                <div class="leaderboard-player">
                    <div class="leaderboard-name">${player.name}</div>
                    <div class="text-xs text-gray-500">${player.position} • ${player.school}</div>
                </div>
                <div class="leaderboard-value">${value} ${unit}</div>
            `;

            // Add click handler to load player in radar chart
            item.addEventListener('click', () => {
                document.getElementById('desktopPlayerSelect').value = player.id;
                this.loadPlayer(player.id);
            });

            container.appendChild(item);
        });
    }

    // Advanced analytics methods
    getPlayerPercentile(playerId, metric) {
        const players = dataManager.getPlayers();
        const playerMetrics = dataManager.getPlayerMetrics(playerId);
        const playerValue = playerMetrics[metric];

        const allValues = players.map(p => {
            const metrics = dataManager.getPlayerMetrics(p.id);
            return metrics[metric];
        }).filter(val => val !== undefined).sort((a, b) => a - b);

        const rank = allValues.findIndex(val => val >= playerValue) + 1;
        const percentile = (rank / allValues.length) * 100;

        return Math.round(percentile);
    }

    generatePlayerReport(playerId) {
        const player = dataManager.getPlayer(playerId);
        if (!player) return null;

        const metrics = dataManager.getPlayerMetrics(playerId);
        const keyMetrics = ['batSpeed', 'exitVelocityAvg', 'attackAngle', 'hardHitAverage'];
        
        const report = {
            player: player,
            metrics: {},
            percentiles: {},
            trends: {},
            strengths: [],
            improvements: []
        };

        keyMetrics.forEach(metric => {
            const trend = dataManager.getPlayerTrend(playerId, metric);
            const percentile = this.getPlayerPercentile(playerId, metric);
            
            report.metrics[metric] = metrics[metric];
            report.percentiles[metric] = percentile;
            report.trends[metric] = trend;

            // Identify strengths (top 25th percentile)
            if (percentile >= 75) {
                report.strengths.push({
                    metric: dataManager.getMetricDisplayName(metric),
                    percentile: percentile,
                    value: dataManager.formatMetricValue(metrics[metric], metric),
                    unit: dataManager.getMetricUnit(metric)
                });
            }

            // Identify areas for improvement (bottom 25th percentile or declining trend)
            if (percentile <= 25 || (trend && trend.trend === 'declining')) {
                report.improvements.push({
                    metric: dataManager.getMetricDisplayName(metric),
                    percentile: percentile,
                    value: dataManager.formatMetricValue(metrics[metric], metric),
                    unit: dataManager.getMetricUnit(metric),
                    trend: trend ? trend.trend : 'stable'
                });
            }
        });

        return report;
    }

    showPlayerReport(playerId) {
        const report = this.generatePlayerReport(playerId);
        if (!report) return;

        // Create and show report modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        
        modal.innerHTML = `
            <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-baseline-black">Performance Report: ${report.player.name}</h2>
                        <button class="close-report text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-4 text-baseline-ocean">Strengths</h3>
                            <div class="space-y-3">
                                ${report.strengths.map(strength => `
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                        <div class="font-semibold text-green-800">${strength.metric}</div>
                                        <div class="text-green-600">${strength.value} ${strength.unit} (${strength.percentile}th percentile)</div>
                                    </div>
                                `).join('')}
                                ${report.strengths.length === 0 ? '<div class="text-gray-500">No standout strengths identified</div>' : ''}
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold mb-4 text-baseline-ocean">Areas for Improvement</h3>
                            <div class="space-y-3">
                                ${report.improvements.map(improvement => `
                                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                        <div class="font-semibold text-orange-800">${improvement.metric}</div>
                                        <div class="text-orange-600">${improvement.value} ${improvement.unit} (${improvement.percentile}th percentile)</div>
                                        ${improvement.trend === 'declining' ? '<div class="text-red-500 text-sm">↓ Declining trend</div>' : ''}
                                    </div>
                                `).join('')}
                                ${report.improvements.length === 0 ? '<div class="text-gray-500">No major areas for improvement identified</div>' : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Bind close event
        modal.querySelector('.close-report').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // Export radar chart data
    exportRadarData() {
        if (!this.currentPlayer || !this.radarChart) return;

        const comparison = dataManager.getPlayerComparison(this.currentPlayer.id, this.currentBenchmark);
        if (!comparison) return;

        const data = {
            player: this.currentPlayer.name,
            benchmark: this.currentBenchmark,
            metrics: comparison.labels.map((label, index) => ({
                metric: label,
                playerValue: comparison.playerData[index],
                benchmarkValue: comparison.benchmarkData[index],
                difference: (comparison.playerData[index] - comparison.benchmarkData[index]).toFixed(1)
            }))
        };

        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.currentPlayer.name.replace(/\s+/g, '_')}_radar_comparison.json`;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // Refresh leaderboards (useful if data changes)
    refreshLeaderboards() {
        this.generateLeaderboards();
    }
}

// Initialize desktop view
const desktopView = new DesktopView();
