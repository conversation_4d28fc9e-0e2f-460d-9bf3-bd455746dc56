// Charts Module
class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultColors = {
            primary: '#1E40AF',
            secondary: '#0EA5E9',
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            gray: '#6b7280'
        };
    }

    createLineChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(44, 44, 44, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0EA5E9',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            return `${context.parsed.y.toFixed(1)} ${options.unit || ''}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                },
                y: {
                    grid: {
                        color: '#f3f4f6'
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8,
                    backgroundColor: '#ffffff',
                    borderWidth: 3,
                    borderColor: this.defaultColors.primary
                },
                line: {
                    borderWidth: 3,
                    borderColor: this.defaultColors.primary,
                    backgroundColor: 'transparent'
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        };

        const config = {
            type: 'line',
            data: data,
            options: { ...defaultOptions, ...options }
        };

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    createRadarChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#374151'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(44, 44, 44, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0EA5E9',
                    borderWidth: 1,
                    cornerRadius: 6
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: {
                        color: '#e5e7eb'
                    },
                    angleLines: {
                        color: '#e5e7eb'
                    },
                    pointLabels: {
                        color: '#374151',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    },
                    ticks: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                },
                line: {
                    borderWidth: 2
                }
            }
        };

        const config = {
            type: 'radar',
            data: data,
            options: { ...defaultOptions, ...options }
        };

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    createMiniLineChart(canvasId, playerData, metric) {
        const trend = dataManager.getPlayerTrend(playerData.id, metric);
        if (!trend) return null;

        const data = {
            labels: trend.timeframes,
            datasets: [{
                data: trend.values,
                borderColor: trend.trend === 'improving' ? this.defaultColors.success : 
                           trend.trend === 'declining' ? this.defaultColors.danger : 
                           this.defaultColors.gray,
                backgroundColor: 'transparent',
                tension: 0.4
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    enabled: true,
                    backgroundColor: 'rgba(44, 44, 44, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0EA5E9',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            const unit = dataManager.getMetricUnit(metric);
                            return `${context.parsed.y.toFixed(1)} ${unit}`;
                        }
                    }
                }
            },
            scales: {
                x: { display: false },
                y: { display: false }
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 5
                }
            }
        };

        return this.createLineChart(canvasId, data, options);
    }

    createPlayerTrendChart(canvasId, playerId, metrics) {
        const timeframes = dataManager.getTimeframes();
        const datasets = metrics.map((metric, index) => {
            const trend = dataManager.getPlayerTrend(playerId, metric);
            if (!trend) return null;

            const colors = [this.defaultColors.primary, this.defaultColors.secondary, this.defaultColors.success, this.defaultColors.warning];
            
            return {
                label: dataManager.getMetricDisplayName(metric),
                data: trend.values,
                borderColor: colors[index % colors.length],
                backgroundColor: 'transparent',
                tension: 0.4,
                borderWidth: 2
            };
        }).filter(dataset => dataset !== null);

        const data = {
            labels: timeframes,
            datasets: datasets
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        color: '#374151'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(44, 44, 44, 0.95)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0EA5E9',
                    borderWidth: 1,
                    cornerRadius: 6
                }
            },
            scales: {
                x: {
                    grid: { display: false },
                    ticks: { color: '#6b7280' }
                },
                y: {
                    grid: { color: '#f3f4f6' },
                    ticks: { color: '#6b7280' }
                }
            }
        };

        return this.createLineChart(canvasId, data, options);
    }

    createPlayerRadarChart(canvasId, playerId, benchmarkAge) {
        const comparison = dataManager.getPlayerComparison(playerId, benchmarkAge);
        if (!comparison) return null;

        const data = {
            labels: comparison.labels,
            datasets: [
                {
                    label: comparison.playerName,
                    data: comparison.playerData,
                    borderColor: this.defaultColors.primary,
                    backgroundColor: this.defaultColors.primary + '20',
                    pointBackgroundColor: this.defaultColors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                },
                {
                    label: 'Peer Average',
                    data: comparison.benchmarkData,
                    borderColor: this.defaultColors.gray,
                    backgroundColor: this.defaultColors.gray + '10',
                    pointBackgroundColor: this.defaultColors.gray,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }
            ]
        };

        return this.createRadarChart(canvasId, data);
    }

    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }

    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }

    // Utility method to normalize data for radar chart (0-100 scale)
    normalizeRadarData(data, metric) {
        const ranges = {
            batSpeed: { min: 60, max: 80 },
            exitVelocityAvg: { min: 65, max: 95 },
            attackAngle: { min: -5, max: 25 },
            onPlaneEfficiency: { min: 50, max: 90 },
            hardHitAverage: { min: 0.150, max: 0.450 }
        };

        const range = ranges[metric];
        if (!range) return data;

        return ((data - range.min) / (range.max - range.min)) * 100;
    }
}

// Initialize global chart manager
const chartManager = new ChartManager();
