// Mobile View Module
class MobileView {
    constructor() {
        this.swiper = null;
        this.currentPlayer = null;
        this.metricCharts = {};
    }

    init() {
        this.setupPlayerSelector();
        this.bindEvents();
    }

    setupPlayerSelector() {
        const playerSelect = document.getElementById('mobilePlayerSelect');
        const players = dataManager.getPlayers();
        
        playerSelect.innerHTML = '<option value="">Select a player...</option>';
        players.forEach(player => {
            const option = document.createElement('option');
            option.value = player.id;
            option.textContent = `${player.name} - ${player.position}`;
            playerSelect.appendChild(option);
        });
    }

    bindEvents() {
        const playerSelect = document.getElementById('mobilePlayerSelect');
        playerSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                this.loadPlayer(parseInt(e.target.value));
            } else {
                this.hidePlayerInfo();
            }
        });
    }

    loadPlayer(playerId) {
        const player = dataManager.getPlayer(playerId);
        if (!player) return;

        this.currentPlayer = player;
        this.showPlayerInfo(player);
        this.createMetricCards(player);
        this.initializeSwiper();
    }

    showPlayerInfo(player) {
        const playerInfo = document.getElementById('mobilePlayerInfo');
        const avatar = document.getElementById('mobilePlayerAvatar');
        const name = document.getElementById('mobilePlayerName');
        const details = document.getElementById('mobilePlayerDetails');

        avatar.src = player.avatar;
        avatar.alt = player.name;
        name.textContent = player.name;
        details.textContent = `${player.age} years old • ${player.position} • ${player.school}`;

        playerInfo.classList.remove('hidden');
        playerInfo.classList.add('fade-in');
    }

    hidePlayerInfo() {
        const playerInfo = document.getElementById('mobilePlayerInfo');
        playerInfo.classList.add('hidden');
        
        const metricCards = document.getElementById('mobileMetricCards');
        metricCards.innerHTML = '';
        
        if (this.swiper) {
            this.swiper.destroy();
            this.swiper = null;
        }
    }

    createMetricCards(player) {
        const container = document.getElementById('mobileMetricCards');
        container.innerHTML = '';

        // Destroy existing charts
        Object.keys(this.metricCharts).forEach(chartId => {
            chartManager.destroyChart(chartId);
        });
        this.metricCharts = {};

        const allMetrics = dataManager.getAllMetrics();
        const priorityMetrics = ['batSpeed', 'exitVelocityAvg', 'attackAngle', 'hardHitAverage', 'onPlaneEfficiency'];
        
        // Show priority metrics first, then others
        const orderedMetrics = [
            ...priorityMetrics,
            ...allMetrics.filter(metric => !priorityMetrics.includes(metric))
        ];

        orderedMetrics.forEach((metric, index) => {
            const card = this.createMetricCard(player, metric, index);
            container.appendChild(card);
        });
    }

    createMetricCard(player, metric, index) {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide';

        const trend = dataManager.getPlayerTrend(player.id, metric);
        const currentValue = dataManager.getPlayerMetrics(player.id)[metric];
        const unit = dataManager.getMetricUnit(metric);
        const displayName = dataManager.getMetricDisplayName(metric);

        const trendClass = trend ? `trend-${trend.trend}` : 'trend-stable';
        const trendLabel = trend ? dataManager.getTrendLabel(trend.trend, trend.change) : '→ No data';

        const chartId = `mobileChart${index}`;

        slide.innerHTML = `
            <div class="metric-card rounded-xl p-6 h-80 flex flex-col">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="metric-label">${displayName}</h3>
                    <span class="trend-indicator ${trendClass}">
                        ${trendLabel}
                    </span>
                </div>
                
                <div class="flex-1 flex flex-col justify-center items-center mb-4">
                    <div class="metric-value mb-2">
                        ${dataManager.formatMetricValue(currentValue, metric)}
                        <span class="text-lg text-gray-500 ml-1">${unit}</span>
                    </div>
                    
                    ${trend ? `
                        <div class="text-sm text-gray-600 mb-4">
                            ${trend.change > 0 ? '+' : ''}${trend.change}% from last period
                        </div>
                    ` : ''}
                </div>
                
                <div class="chart-container h-24">
                    <canvas id="${chartId}" class="w-full h-full"></canvas>
                </div>
            </div>
        `;

        // Create mini chart after DOM insertion
        setTimeout(() => {
            const chart = chartManager.createMiniLineChart(chartId, player, metric);
            if (chart) {
                this.metricCharts[chartId] = chart;
            }
        }, 100);

        return slide;
    }

    initializeSwiper() {
        if (this.swiper) {
            this.swiper.destroy();
        }

        this.swiper = new Swiper('.mobileSwiper', {
            slidesPerView: 1,
            spaceBetween: 20,
            centeredSlides: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true
            },
            keyboard: {
                enabled: true
            },
            mousewheel: {
                enabled: true
            },
            effect: 'slide',
            speed: 300,
            on: {
                slideChange: () => {
                    // Add haptic feedback if available
                    if (navigator.vibrate) {
                        navigator.vibrate(10);
                    }
                }
            }
        });
    }

    // Method to handle metric card interactions
    handleMetricCardTap(metric, value, timeframe) {
        // Show detailed tooltip or modal
        this.showMetricDetail(metric, value, timeframe);
    }

    showMetricDetail(metric, value, timeframe) {
        const unit = dataManager.getMetricUnit(metric);
        const displayName = dataManager.getMetricDisplayName(metric);
        
        // Create a temporary tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-baseline-black text-white p-4 rounded-lg shadow-lg z-50';
        tooltip.innerHTML = `
            <div class="text-center">
                <div class="text-lg font-semibold">${displayName}</div>
                <div class="text-2xl font-bold text-baseline-ocean">${value} ${unit}</div>
                <div class="text-sm opacity-75">${timeframe}</div>
            </div>
        `;
        
        document.body.appendChild(tooltip);
        
        // Remove tooltip after 2 seconds
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 2000);
    }

    // Responsive handling
    handleResize() {
        if (this.swiper) {
            this.swiper.update();
        }
    }

    // Cleanup method
    destroy() {
        if (this.swiper) {
            this.swiper.destroy();
            this.swiper = null;
        }
        
        Object.keys(this.metricCharts).forEach(chartId => {
            chartManager.destroyChart(chartId);
        });
        this.metricCharts = {};
    }
}

// Initialize mobile view
const mobileView = new MobileView();
