// Tablet View Module
class TabletView {
    constructor() {
        this.currentSort = { column: null, direction: 'asc' };
        this.currentFilters = { age: '', position: '', name: '' };
        this.filteredPlayers = [];
    }

    init() {
        this.loadPlayersTable();
        this.bindEvents();
    }

    bindEvents() {
        // Filter events
        document.getElementById('ageFilter').addEventListener('change', (e) => {
            this.currentFilters.age = e.target.value;
            this.applyFilters();
        });

        document.getElementById('positionFilter').addEventListener('change', (e) => {
            this.currentFilters.position = e.target.value;
            this.applyFilters();
        });

        document.getElementById('nameSearch').addEventListener('input', (e) => {
            this.currentFilters.name = e.target.value;
            this.applyFilters();
        });

        // Sort events
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const column = e.target.dataset.sort;
                this.sortTable(column);
            });
        });
    }

    loadPlayersTable() {
        this.filteredPlayers = dataManager.getPlayers();
        this.renderTable();
    }

    applyFilters() {
        this.filteredPlayers = dataManager.filterPlayers(this.currentFilters);
        this.renderTable();
    }

    sortTable(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }

        this.filteredPlayers.sort((a, b) => {
            const aMetrics = dataManager.getPlayerMetrics(a.id);
            const bMetrics = dataManager.getPlayerMetrics(b.id);
            
            let aValue = aMetrics[column] || 0;
            let bValue = bMetrics[column] || 0;

            if (this.currentSort.direction === 'asc') {
                return aValue - bValue;
            } else {
                return bValue - aValue;
            }
        });

        this.updateSortHeaders();
        this.renderTable();
    }

    updateSortHeaders() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (header.dataset.sort === this.currentSort.column) {
                header.classList.add(`sort-${this.currentSort.direction}`);
            }
        });
    }

    renderTable() {
        const tbody = document.getElementById('playersTableBody');
        tbody.innerHTML = '';

        if (this.filteredPlayers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-8 text-gray-500">
                        No players found matching your criteria
                    </td>
                </tr>
            `;
            return;
        }

        this.filteredPlayers.forEach(player => {
            const row = this.createPlayerRow(player);
            tbody.appendChild(row);
        });
    }

    createPlayerRow(player) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 transition-colors';

        const metrics = dataManager.getPlayerMetrics(player.id);
        const batSpeedTrend = dataManager.getPlayerTrend(player.id, 'batSpeed');
        const exitVelTrend = dataManager.getPlayerTrend(player.id, 'exitVelocityAvg');
        const hhatrend = dataManager.getPlayerTrend(player.id, 'hardHitAverage');

        // Calculate overall trend (average of key metrics)
        const trends = [batSpeedTrend, exitVelTrend, hhatrend].filter(t => t);
        const avgChange = trends.length > 0 ? 
            trends.reduce((sum, t) => sum + parseFloat(t.change), 0) / trends.length : 0;
        
        const overallTrend = avgChange > 3 ? 'improving' : avgChange < -3 ? 'declining' : 'stable';

        row.innerHTML = `
            <td class="py-3 px-4">
                <div class="player-info">
                    <img src="${player.avatar}" alt="${player.name}" class="player-avatar">
                    <div>
                        <div class="player-name">${player.name}</div>
                        <div class="player-details">${player.age} • ${player.position} • ${player.school}</div>
                    </div>
                </div>
            </td>
            <td class="py-3 px-4">
                <div class="font-semibold">${dataManager.formatMetricValue(metrics.batSpeed, 'batSpeed')} mph</div>
                ${batSpeedTrend ? `<div class="text-sm text-gray-500">${batSpeedTrend.change > 0 ? '+' : ''}${batSpeedTrend.change}%</div>` : ''}
            </td>
            <td class="py-3 px-4">
                <div class="font-semibold">${dataManager.formatMetricValue(metrics.exitVelocityAvg, 'exitVelocityAvg')} mph</div>
                ${exitVelTrend ? `<div class="text-sm text-gray-500">${exitVelTrend.change > 0 ? '+' : ''}${exitVelTrend.change}%</div>` : ''}
            </td>
            <td class="py-3 px-4">
                <div class="font-semibold">${dataManager.formatMetricValue(metrics.hardHitAverage, 'hardHitAverage')}</div>
                ${hhatrend ? `<div class="text-sm text-gray-500">${hhatrend.change > 0 ? '+' : ''}${hhatrend.change}%</div>` : ''}
            </td>
            <td class="py-3 px-4">
                <span class="trend-indicator trend-${overallTrend}">
                    ${dataManager.getTrendLabel(overallTrend, avgChange.toFixed(1))}
                </span>
            </td>
            <td class="py-3 px-4">
                <button class="action-btn view-details-btn" data-player-id="${player.id}">
                    View Details
                </button>
            </td>
        `;

        // Bind view details event
        const viewBtn = row.querySelector('.view-details-btn');
        viewBtn.addEventListener('click', () => {
            this.showPlayerModal(player.id);
        });

        return row;
    }

    showPlayerModal(playerId) {
        const player = dataManager.getPlayer(playerId);
        if (!player) return;

        const modal = document.getElementById('playerModal');
        const avatar = document.getElementById('modalPlayerAvatar');
        const name = document.getElementById('modalPlayerName');
        const details = document.getElementById('modalPlayerDetails');

        avatar.src = player.avatar;
        avatar.alt = player.name;
        name.textContent = player.name;
        details.textContent = `${player.age} years old • ${player.position} • ${player.school}`;

        // Create charts for the modal
        this.createModalCharts(playerId);

        modal.classList.remove('hidden');
        modal.classList.add('fade-in');

        // Bind close events
        const closeBtn = document.getElementById('closeModal');
        const closeHandler = () => {
            modal.classList.add('hidden');
            modal.classList.remove('fade-in');
            this.destroyModalCharts();
        };

        closeBtn.onclick = closeHandler;
        modal.onclick = (e) => {
            if (e.target === modal) closeHandler();
        };

        // ESC key handler
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                closeHandler();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    createModalCharts(playerId) {
        // Blast Motion metrics chart
        const blastMetrics = ['batSpeed', 'peakHandSpeed', 'attackAngle', 'onPlaneEfficiency'];
        chartManager.createPlayerTrendChart('modalBlastChart', playerId, blastMetrics);

        // HitTrax metrics chart
        const hittraxMetrics = ['exitVelocityAvg', 'distanceAvg', 'hardHitAverage'];
        chartManager.createPlayerTrendChart('modalHittraxChart', playerId, hittraxMetrics);
    }

    destroyModalCharts() {
        chartManager.destroyChart('modalBlastChart');
        chartManager.destroyChart('modalHittraxChart');
    }

    // Export functionality
    exportTableData() {
        const headers = ['Name', 'Age', 'Position', 'School', 'Bat Speed', 'Exit Velocity', 'Hard Hit Average'];
        const rows = this.filteredPlayers.map(player => {
            const metrics = dataManager.getPlayerMetrics(player.id);
            return [
                player.name,
                player.age,
                player.position,
                player.school,
                dataManager.formatMetricValue(metrics.batSpeed, 'batSpeed'),
                dataManager.formatMetricValue(metrics.exitVelocityAvg, 'exitVelocityAvg'),
                dataManager.formatMetricValue(metrics.hardHitAverage, 'hardHitAverage')
            ];
        });

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'baseline_analytics_players.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // Reset filters
    resetFilters() {
        this.currentFilters = { age: '', position: '', name: '' };
        document.getElementById('ageFilter').value = '';
        document.getElementById('positionFilter').value = '';
        document.getElementById('nameSearch').value = '';
        this.applyFilters();
    }
}

// Initialize tablet view
const tabletView = new TabletView();
