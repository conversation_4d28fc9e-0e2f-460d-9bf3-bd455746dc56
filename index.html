<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baseline Analytics - Baseball Performance Demo</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Swiper.js for mobile card navigation -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'baseline-cream': '#F5F5DC',
                        'baseline-black': '#2C2C2C',
                        'baseline-blue': '#1E40AF',
                        'baseline-ocean': '#0EA5E9'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-baseline-cream min-h-screen">
    <!-- Navigation Header -->
    <header class="bg-baseline-black text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-baseline-ocean rounded-lg flex items-center justify-center">
                        <i class="fas fa-baseball-ball text-white text-lg"></i>
                    </div>
                    <h1 class="text-2xl font-bold">Baseline Analytics</h1>
                </div>
                
                <!-- View Toggle -->
                <div class="flex space-x-2">
                    <button id="mobileViewBtn" class="view-btn active px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-mobile-alt mr-2"></i>Mobile
                    </button>
                    <button id="tabletViewBtn" class="view-btn px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-tablet-alt mr-2"></i>Tablet
                    </button>
                    <button id="desktopViewBtn" class="view-btn px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-desktop mr-2"></i>Desktop
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile View -->
    <div id="mobileView" class="view-container">
        <div class="container mx-auto px-4 py-6 max-w-md">
            <!-- Player Selector -->
            <div class="mb-6">
                <select id="mobilePlayerSelect" class="w-full p-3 rounded-lg border border-gray-300 bg-white">
                    <option value="">Select a player...</option>
                </select>
            </div>

            <!-- Player Info Card -->
            <div id="mobilePlayerInfo" class="bg-white rounded-xl shadow-lg p-6 mb-6 hidden">
                <div class="flex items-center space-x-4 mb-4">
                    <img id="mobilePlayerAvatar" class="w-16 h-16 rounded-full object-cover" src="" alt="">
                    <div>
                        <h2 id="mobilePlayerName" class="text-xl font-bold text-baseline-black"></h2>
                        <p id="mobilePlayerDetails" class="text-gray-600"></p>
                    </div>
                </div>
            </div>

            <!-- Swipeable Metric Cards -->
            <div class="swiper mobileSwiper">
                <div class="swiper-wrapper" id="mobileMetricCards">
                    <!-- Metric cards will be dynamically generated -->
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>

    <!-- Tablet View -->
    <div id="tabletView" class="view-container hidden">
        <div class="container mx-auto px-4 py-6">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                    <h2 class="text-2xl font-bold text-baseline-black mb-4 lg:mb-0">Coach Dashboard</h2>
                    
                    <!-- Filters -->
                    <div class="flex flex-wrap gap-3">
                        <select id="ageFilter" class="px-3 py-2 rounded-lg border border-gray-300">
                            <option value="">All Ages</option>
                            <option value="15">Age 15</option>
                            <option value="16">Age 16</option>
                            <option value="17">Age 17</option>
                        </select>
                        <select id="positionFilter" class="px-3 py-2 rounded-lg border border-gray-300">
                            <option value="">All Positions</option>
                            <option value="SS">Shortstop</option>
                            <option value="CF">Center Field</option>
                            <option value="1B">First Base</option>
                            <option value="C">Catcher</option>
                            <option value="3B">Third Base</option>
                            <option value="2B">Second Base</option>
                        </select>
                        <input type="text" id="nameSearch" placeholder="Search by name..." 
                               class="px-3 py-2 rounded-lg border border-gray-300">
                    </div>
                </div>

                <!-- Players Table -->
                <div class="overflow-x-auto">
                    <table id="playersTable" class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-semibold">Player</th>
                                <th class="text-left py-3 px-4 font-semibold cursor-pointer sortable" data-sort="batSpeed">
                                    Bat Speed <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th class="text-left py-3 px-4 font-semibold cursor-pointer sortable" data-sort="exitVelocityAvg">
                                    Exit Velocity <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th class="text-left py-3 px-4 font-semibold cursor-pointer sortable" data-sort="hardHitAverage">
                                    Hard Hit Avg <i class="fas fa-sort ml-1"></i>
                                </th>
                                <th class="text-left py-3 px-4 font-semibold">Trend</th>
                                <th class="text-left py-3 px-4 font-semibold">Action</th>
                            </tr>
                        </thead>
                        <tbody id="playersTableBody">
                            <!-- Table rows will be dynamically generated -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Desktop View -->
    <div id="desktopView" class="view-container hidden">
        <div class="container mx-auto px-4 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Player Selection & Radar Chart -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-2xl font-bold text-baseline-black">Player Benchmarking</h2>
                            <select id="desktopPlayerSelect" class="px-4 py-2 rounded-lg border border-gray-300">
                                <option value="">Select a player...</option>
                            </select>
                        </div>
                        
                        <div class="mb-6">
                            <canvas id="radarChart" width="400" height="400"></canvas>
                        </div>
                        
                        <!-- Benchmark Filters -->
                        <div class="flex flex-wrap gap-3">
                            <select id="benchmarkAge" class="px-3 py-2 rounded-lg border border-gray-300">
                                <option value="age15">Age 15 Benchmark</option>
                                <option value="age16">Age 16 Benchmark</option>
                                <option value="age17">Age 17 Benchmark</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Leaderboard -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-baseline-black mb-4">Leaderboard</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold text-baseline-ocean mb-2">Top Bat Speed</h4>
                                <div id="batSpeedLeaders" class="space-y-2">
                                    <!-- Leaderboard items will be generated -->
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-baseline-ocean mb-2">Top Exit Velocity</h4>
                                <div id="exitVelocityLeaders" class="space-y-2">
                                    <!-- Leaderboard items will be generated -->
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-baseline-ocean mb-2">Top Hard Hit Average</h4>
                                <div id="hardHitLeaders" class="space-y-2">
                                    <!-- Leaderboard items will be generated -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Detail Modal -->
    <div id="playerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <img id="modalPlayerAvatar" class="w-16 h-16 rounded-full object-cover" src="" alt="">
                            <div>
                                <h2 id="modalPlayerName" class="text-2xl font-bold text-baseline-black"></h2>
                                <p id="modalPlayerDetails" class="text-gray-600"></p>
                            </div>
                        </div>
                        <button id="closeModal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Blast Motion Metrics</h3>
                            <canvas id="modalBlastChart" width="400" height="300"></canvas>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-4">HitTrax Metrics</h3>
                            <canvas id="modalHittraxChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Baseline Logo Footer -->
    <footer class="text-center py-4 text-baseline-black">
        <div class="flex items-center justify-center space-x-2">
            <div class="w-6 h-6 bg-baseline-ocean rounded flex items-center justify-center">
                <i class="fas fa-baseball-ball text-white text-xs"></i>
            </div>
            <span class="font-semibold">Baseline Analytics</span>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/mobile.js"></script>
    <script src="js/tablet.js"></script>
    <script src="js/desktop.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
