<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flashy Task Manager</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-tasks"></i> Flashy Task Manager</h1>
            <p>Get things done with style!</p>
        </header>

        <div class="main-content">
            <!-- Add Task Section -->
            <div class="add-task-section">
                <div class="input-group">
                    <input type="text" id="taskInput" placeholder="What needs to be done?" maxlength="100">
                    <select id="categorySelect">
                        <option value="work">🏢 Work</option>
                        <option value="personal">👤 Personal</option>
                        <option value="health">💪 Health</option>
                        <option value="learning">📚 Learning</option>
                        <option value="shopping">🛒 Shopping</option>
                        <option value="other">📝 Other</option>
                    </select>
                    <button id="addTaskBtn" class="add-btn">
                        <i class="fas fa-plus"></i> Add Task
                    </button>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="filter-section">
                <button class="filter-btn active" data-category="all">All Tasks</button>
                <button class="filter-btn" data-category="work">🏢 Work</button>
                <button class="filter-btn" data-category="personal">👤 Personal</button>
                <button class="filter-btn" data-category="health">💪 Health</button>
                <button class="filter-btn" data-category="learning">📚 Learning</button>
                <button class="filter-btn" data-category="shopping">🛒 Shopping</button>
                <button class="filter-btn" data-category="other">📝 Other</button>
            </div>

            <!-- Tasks Container -->
            <div class="tasks-container">
                <div id="tasksList" class="tasks-list">
                    <!-- Tasks will be dynamically added here -->
                </div>
                
                <div class="empty-state" id="emptyState">
                    <i class="fas fa-clipboard-check"></i>
                    <h3>No tasks yet!</h3>
                    <p>Add your first task above to get started.</p>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="stats-section">
            <div class="stat-card">
                <i class="fas fa-list"></i>
                <div>
                    <span class="stat-number" id="totalTasks">0</span>
                    <span class="stat-label">Total Tasks</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-check-circle"></i>
                <div>
                    <span class="stat-number" id="completedTasks">0</span>
                    <span class="stat-label">Completed</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-clock"></i>
                <div>
                    <span class="stat-number" id="pendingTasks">0</span>
                    <span class="stat-label">Pending</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Confetti Canvas for celebrations -->
    <canvas id="confettiCanvas"></canvas>

    <!-- Success Modal -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="celebration-icon">🎉</div>
            <h2>Task Completed!</h2>
            <p id="completionMessage">Great job! You're making progress!</p>
            <button id="closeModal" class="close-btn">Continue</button>
        </div>
    </div>

    <script src="confetti.js"></script>
    <script src="script.js"></script>
</body>
</html>
