// Confetti Animation System
class ConfettiSystem {
    constructor() {
        this.canvas = document.getElementById('confettiCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }
    
    createParticle(x, y) {
        return {
            x: x || Math.random() * this.canvas.width,
            y: y || -10,
            vx: (Math.random() - 0.5) * 10,
            vy: Math.random() * -15 - 5,
            gravity: 0.3,
            friction: 0.99,
            color: this.colors[Math.floor(Math.random() * this.colors.length)],
            size: Math.random() * 8 + 4,
            rotation: Math.random() * 360,
            rotationSpeed: (Math.random() - 0.5) * 10,
            life: 1,
            decay: Math.random() * 0.02 + 0.01,
            shape: Math.random() > 0.5 ? 'circle' : 'square'
        };
    }
    
    burst(x, y, count = 50) {
        for (let i = 0; i < count; i++) {
            this.particles.push(this.createParticle(x, y));
        }
        this.animate();
    }
    
    celebrate() {
        // Create multiple bursts across the screen
        const burstCount = 5;
        for (let i = 0; i < burstCount; i++) {
            setTimeout(() => {
                const x = Math.random() * this.canvas.width;
                const y = Math.random() * this.canvas.height * 0.3;
                this.burst(x, y, 30);
            }, i * 200);
        }
    }
    
    update() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update physics
            particle.vy += particle.gravity;
            particle.vx *= particle.friction;
            particle.vy *= particle.friction;
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.rotation += particle.rotationSpeed;
            particle.life -= particle.decay;
            
            // Remove dead particles
            if (particle.life <= 0 || particle.y > this.canvas.height + 100) {
                this.particles.splice(i, 1);
            }
        }
    }
    
    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        for (const particle of this.particles) {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.translate(particle.x, particle.y);
            this.ctx.rotate(particle.rotation * Math.PI / 180);
            
            this.ctx.fillStyle = particle.color;
            
            if (particle.shape === 'circle') {
                this.ctx.beginPath();
                this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
                this.ctx.fill();
            } else {
                this.ctx.fillRect(-particle.size/2, -particle.size/2, particle.size, particle.size);
            }
            
            this.ctx.restore();
        }
    }
    
    animate() {
        if (this.particles.length > 0) {
            this.update();
            this.draw();
            requestAnimationFrame(() => this.animate());
        }
    }
}

// Particle burst effect for task completion
class ParticleBurst {
    constructor(element) {
        this.element = element;
        this.particles = [];
    }
    
    create() {
        const rect = this.element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // Create floating particles around the completed task
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            particle.style.cssText = `
                position: fixed;
                width: 6px;
                height: 6px;
                background: ${this.getRandomColor()};
                border-radius: 50%;
                pointer-events: none;
                z-index: 999;
                left: ${centerX}px;
                top: ${centerY}px;
                animation: floatAway ${Math.random() * 2 + 1}s ease-out forwards;
            `;
            
            // Random direction
            const angle = (Math.PI * 2 * i) / 20;
            const distance = Math.random() * 100 + 50;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance - Math.random() * 50;
            
            particle.style.setProperty('--end-x', `${endX}px`);
            particle.style.setProperty('--end-y', `${endY}px`);
            
            document.body.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 2000);
        }
    }
    
    getRandomColor() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
}

// Add CSS for floating particles
const style = document.createElement('style');
style.textContent = `
    @keyframes floatAway {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translate(calc(var(--end-x) - var(--start-x, 0px)), calc(var(--end-y) - var(--start-y, 0px))) scale(0);
            opacity: 0;
        }
    }
    
    .task-item.completing {
        animation: taskComplete 0.6s ease;
    }
    
    @keyframes taskComplete {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); background: #e8f5e8; }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Initialize confetti system when DOM is loaded
let confettiSystem;
document.addEventListener('DOMContentLoaded', () => {
    confettiSystem = new ConfettiSystem();
});
